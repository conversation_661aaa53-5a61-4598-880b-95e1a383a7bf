#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档到Markdown转换器 (macOS版本)
运行环境：conda activate mineru

功能：
1. 将Word文档(.docx)转换为Markdown格式
2. 保持原文档的排版格式
3. 转换图表和图片，保持位置一致
4. 支持大文件拆分和合并
5. 确保转换质量

Author: kangrl
Date: 2025-01-21
"""

import sys
import logging
from pathlib import Path
from typing import List, Dict
import zipfile
from datetime import datetime

try:
    # 文档处理库
    from docx import Document
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请在mineru环境中安装以下依赖:")
    print("conda activate mineru")
    print("pip install python-docx pillow markdownify pandas numpy")
    sys.exit(1)


class WordToMarkdownConverter:
    """Word文档到Markdown转换器 (macOS版本)"""

    def __init__(self, input_folder: str, output_folder: str = None):
        """
        初始化转换器

        Args:
            input_folder: 输入文件夹路径
            output_folder: 输出文件夹路径，默认为输入文件夹下的markdown子目录
        """
        self.input_folder = Path(input_folder)
        self.output_folder = (Path(output_folder) if output_folder
                              else self.input_folder / "markdown")
        self.images_folder = self.output_folder / "images"

        # 创建输出目录
        self.output_folder.mkdir(parents=True, exist_ok=True)
        self.images_folder.mkdir(parents=True, exist_ok=True)

        # 设置日志
        self._setup_logging()

        # 大文件阈值 (10MB)
        self.large_file_threshold = 10 * 1024 * 1024

        # 支持的文件格式 (macOS版本只支持docx)
        self.supported_formats = ['.docx']

        self.logger.info(f"初始化转换器 - 输入目录: {self.input_folder}")
        self.logger.info(f"输出目录: {self.output_folder}")
        self.logger.info("注意: macOS版本暂不支持.doc文件，请先转换为.docx格式")

    def _setup_logging(self):
        """设置日志配置"""
        log_file = self.output_folder / "conversion.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_word_files(self) -> List[Path]:
        """获取所有Word文件"""
        word_files = []

        # 查找.docx文件
        word_files.extend(self.input_folder.glob("*.docx"))

        # 查找.doc文件并提示用户
        doc_files = list(self.input_folder.glob("*.doc"))
        if doc_files:
            self.logger.warning("发现.doc文件，但macOS版本不支持直接转换:")
            for doc_file in doc_files:
                self.logger.warning(f"  - {doc_file.name}")
            self.logger.warning("请使用Word软件将.doc文件另存为.docx格式后重新运行")

        # 按文件大小排序，大文件在后
        word_files.sort(key=lambda x: x.stat().st_size)

        self.logger.info(f"找到 {len(word_files)} 个可处理的Word文件")
        for file in word_files:
            size_mb = file.stat().st_size / (1024 * 1024)
            self.logger.info(f"  - {file.name} ({size_mb:.1f}MB)")

        return word_files

    def extract_images_from_docx(self, docx_file: Path) -> Dict[str, str]:
        """从docx文件中提取图片"""
        image_map = {}

        try:
            # 解压docx文件
            with zipfile.ZipFile(docx_file, 'r') as zip_ref:
                # 查找图片文件
                image_files = [f for f in zip_ref.namelist()
                              if f.startswith('word/media/')]

                for img_file in image_files:
                    try:
                        # 提取图片数据
                        img_data = zip_ref.read(img_file)

                        # 生成图片文件名
                        img_name = f"{docx_file.stem}_{Path(img_file).name}"
                        img_path = self.images_folder / img_name

                        # 保存图片
                        with open(img_path, 'wb') as f:
                            f.write(img_data)

                        # 记录图片映射
                        image_map[img_file] = f"images/{img_name}"

                        self.logger.info(f"提取图片: {img_name}")

                    except Exception as e:
                        self.logger.warning(f"提取图片失败 {img_file}: {e}")

        except Exception as e:
            self.logger.error(f"解压docx文件失败 {docx_file.name}: {e}")

        return image_map

    def convert_table_to_markdown(self, table) -> str:
        """将Word表格转换为Markdown表格"""
        try:
            rows = []

            # 处理表格行
            for i, row in enumerate(table.rows):
                cells = []
                for cell in row.cells:
                    # 清理单元格文本
                    cell_text = cell.text.strip().replace('\n', '<br>')
                    if not cell_text:
                        cell_text = " "  # 空单元格用空格填充
                    cells.append(cell_text)
                rows.append(cells)

            if not rows:
                return ""

            # 生成Markdown表格
            markdown_table = []

            # 表头
            if rows:
                header = "| " + " | ".join(rows[0]) + " |"
                markdown_table.append(header)

                # 分隔线
                separator = "| " + " | ".join(["---"] * len(rows[0])) + " |"
                markdown_table.append(separator)

                # 数据行
                for row in rows[1:]:
                    # 确保每行有相同数量的列
                    while len(row) < len(rows[0]):
                        row.append(" ")
                    data_row = "| " + " | ".join(row[:len(rows[0])]) + " |"
                    markdown_table.append(data_row)

            return "\n".join(markdown_table) + "\n"

        except Exception as e:
            self.logger.warning(f"表格转换失败: {e}")
            return ""

    def process_paragraph(self, paragraph, image_map: Dict[str, str]) -> str:
        """处理段落，包括文本格式和图片"""
        if not paragraph.text.strip() and not paragraph._element.xpath('.//w:drawing'):
            return ""

        text_parts = []

        # 处理段落中的运行（runs）
        for run in paragraph.runs:
            text = run.text

            # 应用格式
            if run.bold:
                text = f"**{text}**"
            if run.italic:
                text = f"*{text}*"
            if run.underline:
                text = f"<u>{text}</u>"

            text_parts.append(text)

        paragraph_text = "".join(text_parts)

        # 检查段落中的图片
        drawings = paragraph._element.xpath('.//w:drawing')
        if drawings:
            # 为每个图片添加占位符
            for i, drawing in enumerate(drawings):
                # 尝试获取图片的描述信息
                image_desc = "图片"
                try:
                    # 查找图片描述
                    desc_elements = drawing.xpath('.//wp:docPr/@descr')
                    if desc_elements:
                        image_desc = desc_elements[0]
                except:
                    pass

                paragraph_text += f"\n\n![{image_desc}]()\n\n"

        # 应用段落对齐
        if paragraph.alignment == WD_PARAGRAPH_ALIGNMENT.CENTER:
            paragraph_text = f"<div align='center'>{paragraph_text}</div>"
        elif paragraph.alignment == WD_PARAGRAPH_ALIGNMENT.RIGHT:
            paragraph_text = f"<div align='right'>{paragraph_text}</div>"
        elif paragraph.alignment == WD_PARAGRAPH_ALIGNMENT.JUSTIFY:
            paragraph_text = f"<div align='justify'>{paragraph_text}</div>"

        return paragraph_text

    def convert_docx_to_markdown(self, docx_file: Path) -> str:
        """将docx文件转换为Markdown"""
        try:
            doc = Document(docx_file)
            markdown_content = []

            # 提取图片
            image_map = self.extract_images_from_docx(docx_file)

            # 处理文档元素
            for element in doc.element.body:
                if element.tag.endswith('p'):  # 段落
                    para = None
                    for p in doc.paragraphs:
                        if p._element == element:
                            para = p
                            break

                    if para:
                        para_text = self.process_paragraph(para, image_map)
                        if para_text:
                            # 判断是否为标题
                            if para.style.name.startswith('Heading'):
                                try:
                                    level_str = para.style.name.split()[-1]
                                    level = int(level_str) if level_str.isdigit() else 1
                                    level = min(level, 6)  # 最大6级标题
                                    markdown_content.append(f"{'#' * level} {para_text}\n")
                                except:
                                    markdown_content.append(f"# {para_text}\n")
                            else:
                                markdown_content.append(f"{para_text}\n")

                elif element.tag.endswith('tbl'):  # 表格
                    table = None
                    for t in doc.tables:
                        if t._element == element:
                            table = t
                            break

                    if table:
                        table_md = self.convert_table_to_markdown(table)
                        if table_md:
                            markdown_content.append(f"\n{table_md}\n")

            return "\n".join(markdown_content)

        except Exception as e:
            self.logger.error(f"转换docx到Markdown失败 {docx_file.name}: {e}")
            return ""

    def split_large_file(self, file_path: Path, content: str,
                        max_size: int = 5000000) -> List[str]:
        """拆分大文件内容"""
        if len(content.encode('utf-8')) <= max_size:
            return [content]

        parts = []
        lines = content.split('\n')
        current_part = []
        current_size = 0

        for line in lines:
            line_size = len(line.encode('utf-8'))

            if current_size + line_size > max_size and current_part:
                # 保存当前部分
                parts.append('\n'.join(current_part))
                current_part = [line]
                current_size = line_size
            else:
                current_part.append(line)
                current_size += line_size

        # 添加最后一部分
        if current_part:
            parts.append('\n'.join(current_part))

        self.logger.info(f"大文件 {file_path.name} 拆分为 {len(parts)} 个部分")
        return parts

    def merge_markdown_files(self, output_files: List[Path]) -> Path:
        """合并拆分的Markdown文件"""
        if len(output_files) <= 1:
            return output_files[0] if output_files else None

        merged_file = self.output_folder / "merged_documents.md"

        try:
            with open(merged_file, 'w', encoding='utf-8') as f:
                f.write("# 异常工况汇编 - 合并文档\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("---\n\n")

                for i, file_path in enumerate(output_files, 1):
                    f.write(f"## 文档 {i}: {file_path.stem}\n\n")

                    try:
                        with open(file_path, 'r', encoding='utf-8') as part_file:
                            content = part_file.read()
                            f.write(content)
                            f.write("\n\n---\n\n")
                    except Exception as e:
                        self.logger.error(f"读取文件失败 {file_path}: {e}")
                        f.write(f"**错误: 无法读取文件 {file_path.name}**\n\n")

            self.logger.info(f"已生成合并文档: {merged_file.name}")
            return merged_file

        except Exception as e:
            self.logger.error(f"合并文档失败: {e}")
            return None

    def convert_single_file(self, file_path: Path) -> List[Path]:
        """转换单个Word文件"""
        self.logger.info(f"开始转换文件: {file_path.name}")

        try:
            # 转换为Markdown
            markdown_content = self.convert_docx_to_markdown(file_path)

            if not markdown_content:
                self.logger.warning(f"文件转换为空: {file_path.name}")
                return []

            # 检查文件大小，决定是否拆分
            file_size = len(markdown_content.encode('utf-8'))

            if file_size > self.large_file_threshold:
                # 拆分大文件
                parts = self.split_large_file(file_path, markdown_content)
                output_files = []

                for i, part in enumerate(parts, 1):
                    output_file = self.output_folder / f"{file_path.stem}_part_{i}.md"

                    with open(output_file, 'w', encoding='utf-8') as f:
                        # 添加文件头
                        f.write(f"# {file_path.stem} - 第{i}部分\n\n")
                        f.write(f"原文件: {file_path.name}\n")
                        f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"部分: {i}/{len(parts)}\n\n")
                        f.write("---\n\n")
                        f.write(part)

                    output_files.append(output_file)
                    self.logger.info(f"已生成部分文件: {output_file.name}")

                return output_files

            else:
                # 单个文件
                output_file = self.output_folder / f"{file_path.stem}.md"

                with open(output_file, 'w', encoding='utf-8') as f:
                    # 添加文件头
                    f.write(f"# {file_path.stem}\n\n")
                    f.write(f"原文件: {file_path.name}\n")
                    f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"文件大小: {file_size/1024:.1f} KB\n\n")
                    f.write("---\n\n")
                    f.write(markdown_content)

                self.logger.info(f"已生成Markdown文件: {output_file.name}")
                return [output_file]

        except Exception as e:
            self.logger.error(f"转换文件失败 {file_path.name}: {e}")
            return []

    def convert_all_files(self) -> None:
        """转换所有Word文件"""
        word_files = self.get_word_files()

        if not word_files:
            self.logger.warning("未找到可处理的Word文件")
            return

        all_output_files = []
        conversion_summary = []

        for file_path in word_files:
            start_time = datetime.now()
            output_files = self.convert_single_file(file_path)
            end_time = datetime.now()

            duration = (end_time - start_time).total_seconds()

            if output_files:
                all_output_files.extend(output_files)
                conversion_summary.append({
                    'source_file': file_path.name,
                    'output_files': [f.name for f in output_files],
                    'duration': f"{duration:.2f}s",
                    'status': 'SUCCESS'
                })
            else:
                conversion_summary.append({
                    'source_file': file_path.name,
                    'output_files': [],
                    'duration': f"{duration:.2f}s",
                    'status': 'FAILED'
                })

        # 生成转换总结
        self.generate_conversion_summary(conversion_summary)

        # 合并所有转换的文件
        if len(all_output_files) > 1:
            merged_file = self.merge_markdown_files(all_output_files)
            if merged_file:
                all_output_files.append(merged_file)

        self.logger.info(f"转换完成! 共生成 {len(all_output_files)} 个Markdown文件")

    def generate_conversion_summary(self, summary_data: List[Dict]) -> None:
        """生成转换总结报告"""
        summary_file = self.output_folder / "conversion_summary.md"

        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("# Word文档转换总结报告 (macOS版本)\n\n")
                f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"输入目录: {self.input_folder}\n")
                f.write(f"输出目录: {self.output_folder}\n\n")

                # 统计信息
                total_files = len(summary_data)
                success_files = len([s for s in summary_data if s['status'] == 'SUCCESS'])
                failed_files = total_files - success_files

                f.write("## 转换统计\n\n")
                f.write(f"- 总文件数: {total_files}\n")
                f.write(f"- 成功转换: {success_files}\n")
                f.write(f"- 转换失败: {failed_files}\n")
                if total_files > 0:
                    f.write(f"- 成功率: {success_files/total_files*100:.1f}%\n\n")

                # 详细结果
                f.write("## 转换详情\n\n")
                f.write("| 源文件 | 输出文件 | 耗时 | 状态 |\n")
                f.write("|--------|----------|------|------|\n")

                for item in summary_data:
                    output_files_str = '<br>'.join(item['output_files']) if item['output_files'] else '无'
                    status_icon = "✅" if item['status'] == 'SUCCESS' else "❌"

                    f.write(f"| {item['source_file']} | {output_files_str} | {item['duration']} | {status_icon} {item['status']} |\n")

                f.write("\n## 注意事项\n\n")
                f.write("1. 图片已提取到 `images/` 目录\n")
                f.write("2. 大文件已自动拆分为多个部分\n")
                f.write("3. 如有转换失败的文件，请检查日志文件\n")
                f.write("4. 合并文档包含所有成功转换的内容\n")
                f.write("5. macOS版本暂不支持.doc文件，请先转换为.docx格式\n")

            self.logger.info(f"已生成转换总结: {summary_file.name}")

        except Exception as e:
            self.logger.error(f"生成转换总结失败: {e}")


def main():
    """主函数"""
    try:
        # 输入和输出目录
        input_folder = "/Users/<USER>/Local/WorkSpace/GitLab/宁夏煤业/异常工况汇编"
        output_folder = "/Users/<USER>/Local/WorkSpace/GitLab/宁夏煤业/异常工况汇编/markdown"

        print("="*60)
        print("Word文档到Markdown转换器 (macOS版本)")
        print("运行环境: conda mineru")
        print("="*60)

        # 创建转换器
        converter = WordToMarkdownConverter(input_folder, output_folder)

        # 执行转换
        converter.convert_all_files()

        print("\n转换完成! 请查看输出目录中的结果文件。")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

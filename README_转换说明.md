# Word文档到Markdown转换器使用说明

## 概述
本工具专门用于将异常工况汇编文件夹中的Office Word文档(.doc/.docx)转换为Markdown格式，满足以下要求：
1. 排版格式与源文件保持一致
2. 图表进行严格转换与源文件一致
3. 图片位置与源文件一致
4. 超大文件自动拆分后合并
5. 优先保证转换质量

## 运行环境
- **必需环境**: conda的mineru环境
- **Python版本**: 3.7+
- **操作系统**: Windows/macOS/Linux

## 安装步骤

### 1. 激活mineru环境
```bash
conda activate mineru
```

### 2. 安装依赖
```bash
python install_dependencies.py
```

或手动安装：
```bash
pip install python-docx pillow markdownify pandas numpy lxml beautifulsoup4
# Windows用户还需要安装：
pip install pywin32
```

## 使用方法

### 1. 直接运行（推荐）
```bash
conda activate mineru
python word_to_markdown_converter.py
```

### 2. 自定义路径运行
修改`word_to_markdown_converter.py`中的路径设置：
```python
# 在main()函数中修改这两行
input_folder = "你的输入文件夹路径"
output_folder = "你的输出文件夹路径"
```

## 功能特性

### 📁 文件处理
- **支持格式**: `.doc`, `.docx`
- **自动转换**: `.doc`文件自动转换为`.docx`后处理
- **批量处理**: 一次性处理整个文件夹

### 📊 格式保持
- **段落格式**: 保持原文档的段落对齐方式
- **文本格式**: 保持粗体、斜体、下划线等格式
- **标题层级**: 自动识别并转换标题层级
- **表格转换**: Word表格转换为Markdown表格格式

### 🖼️ 图片处理
- **图片提取**: 自动从docx文件中提取所有图片
- **路径管理**: 图片保存到`images/`目录，自动生成引用路径
- **格式支持**: 支持常见图片格式(jpg, png, gif等)

### 📄 大文件处理
- **智能拆分**: 超过10MB的文件自动拆分为多个部分
- **自动合并**: 生成包含所有文档的合并文件
- **拆分阈值**: 默认5MB每个分片，可自定义

### 📊 转换报告
- **转换总结**: 生成详细的转换统计报告
- **成功率统计**: 显示转换成功和失败的文件数量
- **详细日志**: 记录每个文件的转换过程和错误信息

## 输出结构

转换完成后，输出目录结构如下：
```
markdown/
├── images/                          # 提取的图片文件
│   ├── 文档1_image1.jpg
│   ├── 文档1_image2.png
│   └── ...
├── 文档1.md                        # 转换后的Markdown文件
├── 文档2_part_1.md                 # 大文件的第1部分
├── 文档2_part_2.md                 # 大文件的第2部分
├── merged_documents.md              # 所有文档的合并文件
├── conversion_summary.md            # 转换总结报告
└── conversion.log                   # 详细转换日志
```

## 转换质量保证

### ✅ 支持的元素
- 段落文本（包括格式）
- 标题（1-6级）
- 表格（自动生成Markdown表格）
- 图片（提取并生成引用）
- 文本对齐（居中、右对齐等）

### ⚠️ 注意事项
- 复杂的页面布局可能需要手动调整
- 某些Word特殊格式在Markdown中无对应项
- 嵌入对象（如Excel表格）需要特殊处理
- 页眉页脚信息可能丢失

## 故障排除

### 常见问题

1. **`.doc文件转换失败`**
   - 确保在Windows环境下运行
   - 检查是否安装了Microsoft Word
   - 尝试手动将.doc转换为.docx

2. **图片提取失败`**
   - 检查docx文件是否损坏
   - 确保有足够的磁盘空间
   - 检查images目录的写权限

3. **转换后格式混乱`**
   - 检查原文档是否有复杂的样式
   - 考虑简化原文档格式后重新转换
   - 手动调整输出的Markdown文件

4. **依赖安装失败`**
   - 确保已激活mineru环境
   - 检查网络连接
   - 尝试使用国内镜像源安装

### 日志查看
转换过程中的详细信息记录在`conversion.log`文件中：
```bash
# 查看转换日志
tail -f markdown/conversion.log

# 查看错误信息
grep "ERROR" markdown/conversion.log
```

## 高级配置

### 自定义拆分阈值
在`WordToMarkdownConverter`类中修改：
```python
# 修改大文件阈值为20MB
self.large_file_threshold = 20 * 1024 * 1024
```

### 自定义分片大小
在`split_large_file`方法中修改：
```python
def split_large_file(self, file_path: Path, content: str, max_size: int = 10000000):
    # max_size改为10MB
```

## 技术支持

- **作者**: kangrl
- **邮箱**: <EMAIL>
- **版本**: 1.0
- **更新日期**: 2025-01-21

## 版本历史

### v1.0 (2025-01-21)
- 首次发布
- 支持Word文档批量转换
- 支持图片提取和表格转换
- 支持大文件拆分和合并
- 生成详细的转换报告

---

**注意**: 本工具基于conda的mineru环境开发，请确保在正确的环境中运行。

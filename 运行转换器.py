#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word转Markdown快速启动脚本
运行环境：conda activate mineru

Author: kangrl
Date: 2025-01-21
"""

import os
import sys
from pathlib import Path

def main():
    """快速启动转换器"""
    print("="*60)
    print("Word文档到Markdown转换器 - 快速启动")
    print("运行环境: conda mineru")
    print("="*60)

    # 检查是否在正确的目录
    current_dir = Path.cwd()

    # 查找转换器脚本
    converter_script = None
    for script_name in ["word_to_markdown_converter_mac.py", "word_to_markdown_converter.py"]:
        if (current_dir / script_name).exists():
            converter_script = script_name
            break

    if not converter_script:
        print("❌ 错误: 未找到转换器脚本文件")
        print("请确保以下文件存在:")
        print("  - word_to_markdown_converter_mac.py (macOS版本)")
        print("  - word_to_markdown_converter.py (Windows版本)")
        return

    print(f"✅ 找到转换器脚本: {converter_script}")

    # 检查异常工况汇编目录
    input_dir = current_dir / "异常工况汇编"
    if not input_dir.exists():
        print("❌ 错误: 未找到'异常工况汇编'目录")
        print(f"当前目录: {current_dir}")
        return

    print(f"✅ 找到输入目录: {input_dir}")

    # 运行转换器
    print("\n开始执行转换...")
    print("-" * 40)

    try:
        os.system(f"python {converter_script}")

        print("-" * 40)
        print("✅ 转换执行完成!")

        # 检查输出结果
        output_dir = input_dir / "markdown"
        if output_dir.exists():
            print(f"\n📁 输出目录: {output_dir}")

            # 列出生成的文件
            md_files = list(output_dir.glob("*.md"))
            if md_files:
                print(f"📄 生成的Markdown文件 ({len(md_files)}个):")
                for md_file in md_files:
                    size_kb = md_file.stat().st_size / 1024
                    print(f"  - {md_file.name} ({size_kb:.1f}KB)")

            # 检查图片目录
            images_dir = output_dir / "images"
            if images_dir.exists():
                image_files = list(images_dir.glob("*"))
                print(f"🖼️ 提取的图片文件: {len(image_files)}个")

        print("\n🎉 转换完成! 请查看markdown目录中的结果文件。")

    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")


if __name__ == "__main__":
    main()

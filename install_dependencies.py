#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word转Markdown依赖安装脚本
运行环境：conda activate mineru

Author: kangrl
Date: 2025-01-21
"""

import subprocess
import sys
import platform


def run_command(command):
    """执行命令并返回结果"""
    try:
        subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ 成功: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {command}")
        print(f"错误信息: {e.stderr}")
        return False


def install_dependencies():
    """安装依赖包"""
    print("="*60)
    print("Word转Markdown依赖安装程序")
    print("运行环境: conda mineru")
    print("="*60)

    # 检查Python版本
    python_version = sys.version
    print(f"Python版本: {python_version}")

    # 检查操作系统
    os_info = platform.system()
    print(f"操作系统: {os_info}")

    # 基础依赖
    dependencies = [
        "python-docx",
        "pillow",
        "markdownify",
        "pandas",
        "numpy",
        "lxml",
        "beautifulsoup4"
    ]

    # Windows特定依赖
    if os_info == "Windows":
        dependencies.append("pywin32")

    print("\n开始安装依赖包...")

    failed_packages = []

    for package in dependencies:
        print(f"\n安装 {package}...")
        if not run_command(f"pip install {package}"):
            failed_packages.append(package)

    # 安装结果汇总
    print("\n" + "="*60)
    print("安装结果汇总")
    print("="*60)

    success_count = len(dependencies) - len(failed_packages)
    print(f"成功安装: {success_count}/{len(dependencies)} 个包")

    if failed_packages:
        print(f"安装失败的包: {', '.join(failed_packages)}")
        print("\n请手动安装失败的包:")
        for pkg in failed_packages:
            print(f"  pip install {pkg}")
    else:
        print("✅ 所有依赖包安装成功!")

    print("\n安装完成后，可以运行转换脚本:")
    print("  python word_to_markdown_converter.py")


if __name__ == "__main__":
    install_dependencies()
